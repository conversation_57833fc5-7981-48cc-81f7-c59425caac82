// Simple test to verify the transformCreationDateWeek function works
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek.js';
import timezone from 'dayjs/plugin/timezone.js';
import utc from 'dayjs/plugin/utc.js';

// Configure dayjs
dayjs.extend(isoWeek);
dayjs.extend(timezone);
dayjs.extend(utc);

function transformCreationDateWeek(isoDateString) {
  try {
    // Get user's timezone (for testing, we'll use UTC)
    const userTimeZone = 'UTC';

    // Parse the ISO date string and convert to user's timezone
    const date = dayjs(isoDateString).tz(userTimeZone);

    // Check if the date is valid
    if (!date.isValid()) {
      return 'Invalid Date';
    }

    // Get ISO week number and year
    const weekNumber = date.isoWeek();
    const year = date.isoWeekYear(); // Use isoWeekYear for correct year

    return `Week ${weekNumber}, ${year}`;
  } catch {
    // Fallback for invalid dates
    return 'Invalid Date';
  }
}

// Test cases
console.log('Testing transformCreationDateWeek function:');
console.log('1. February 17, 2025:', transformCreationDateWeek('2025-02-17T00:00:00.000+00:00'));
console.log('2. January 1, 2025:', transformCreationDateWeek('2025-01-01T00:00:00.000+00:00'));
console.log('3. December 30, 2024:', transformCreationDateWeek('2024-12-30T00:00:00.000+00:00'));
console.log('4. Invalid date:', transformCreationDateWeek('invalid-date'));
console.log('5. Empty string:', transformCreationDateWeek(''));
console.log('6. Different timezone:', transformCreationDateWeek('2025-02-17T12:30:45.123+02:00'));
