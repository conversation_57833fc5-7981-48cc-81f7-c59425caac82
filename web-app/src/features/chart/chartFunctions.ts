import dayjs from 'dayjs';
import { ChartConfigurationFormInput, ChartSetting, paletteColors, RollingPeriod } from './chartTypes';
import { themeToColor } from '../../theme';
import { hasMetaMap, KeyTotal, StatsGroupBy, StatsGroupByFieldMetaMap } from '../stats/statsTypes';
import { transformCreationDateWeek } from '../work-permit/workPermitTypes';

export default function getDateRangeFromRollingPeriod(period: RollingPeriod): { startDate: number; endDate: number } {
  const end = dayjs().endOf('day');
  let start: dayjs.Dayjs;

  switch (period) {
    case RollingPeriod.LAST_7_DAYS:
      start = end.subtract(7, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_30_DAYS:
      start = end.subtract(30, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_90_DAYS:
      start = end.subtract(90, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_6_MONTHS:
      start = end.subtract(6, 'months').startOf('day');
      break;
    case RollingPeriod.LAST_YEAR:
      start = end.subtract(1, 'year').startOf('day');
      break;
    default:
      start = end.subtract(7, 'days').startOf('day');
  }

  return {
    startDate: start.valueOf(),
    endDate: end.valueOf(),
  };
}

export function randomThemeColor(): string {
  return themeToColor(paletteColors[Math.floor(Math.random() * paletteColors.length)]);
}

/**
 * Get a color for a key based on various color mapping strategies
 *
 * @param key The key to get a color for
 * @param fieldColorMapping Optional user-defined mapping of fields to colors
 * @param groupBy Optional grouping parameter to look up predefined colors from metadata
 * @returns The color for the key - either from fieldColorMapping, metadata, or a random theme color
 */
export function getFieldColor(key: string, fieldColorMapping?: Record<string, string>, groupBy?: StatsGroupBy): string {
  if (fieldColorMapping?.[key]) {
    return fieldColorMapping[key];
  }

  if (!groupBy || !hasMetaMap(groupBy)) {
    return randomThemeColor();
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return randomThemeColor();
  }

  return metaValue.color;
}
/**
 * Get a display name for a key based on metadata mapping or special transformations
 *
 * @param key The key to get a display name for
 * @param groupBy Optional grouping parameter to look up predefined names from metadata
 * @returns The display name for the key - either from metadata, transformation, or the original key
 */
export function getFieldName(key: string, groupBy?: StatsGroupBy): string {
  // Handle special date-based transformations
  if (groupBy && isDateWeekGroupBy(groupBy)) {
    return transformCreationDateWeek(key);
  }

  if (!groupBy || !hasMetaMap(groupBy)) {
    return key;
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return key;
  }

  return metaValue.label;
}

/**
 * Check if a groupBy field is a date-week based grouping that requires transformation
 */
function isDateWeekGroupBy(groupBy: StatsGroupBy): boolean {
  return groupBy.endsWith('_CREATION_DATE_WEEK') ||
         groupBy.endsWith('_DATE_WEEK') ||
         groupBy.endsWith('_PLANNED_DATE_WEEK');
}

// helper to elide with an ellipsis:
export const truncate = (s: string, max: number) => (max > 0 && s.length > max ? `${s.slice(0, max)}…` : s);

// ? Helper to extract the value for a given key
export const extractY = (total: number | KeyTotal[], key: string) => {
  const items = Array.isArray(total) ? total : [{ key, total }];
  return items.find((item) => item.key === key)?.total ?? 0;
};

export const convertChartSettingToForm = (chartSetting: ChartSetting): ChartConfigurationFormInput => ({
  ...chartSetting,
});

export const convertFormToChartSetting = (form: ChartConfigurationFormInput): ChartSetting => {
  const { fieldArrayId, ...rest } = form;
  const entries = Object.entries(rest).filter(([, v]) => v != null);
  return Object.fromEntries(entries) as unknown as ChartSetting;
};
